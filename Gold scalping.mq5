//+------------------------------------------------------------------+
//|                                  Enhanced Gold Scalping EA.mq5 |
//|                        Copyright 2024, Enhanced Gold Strategy   |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Enhanced Gold Strategy"
#property link      "https://www.mql5.com"
#property version   "2.00"
#property description "Enhanced Gold Scalping EA with advanced filters and dynamic management"

//--- Include trade class
#include <Trade\Trade.mqh>

//--- Input parameters
input group "=== Strategy Parameters ==="
input int InpEMA_Fast = 21;                    // Fast EMA Period (optimized for M1)
input int InpEMA_Medium = 50;                  // Medium EMA Period
input int InpEMA_Slow = 200;                   // Slow EMA Period
input int InpRSI_Period = 9;                   // RSI Period (faster for M1)
input int InpATR_Period = 14;                  // ATR Period
input double InpRSI_Lower = 40.0;              // RSI Lower Bound
input double InpRSI_Upper = 60.0;              // RSI Upper Bound

input group "=== Advanced Filters ==="
input bool InpUseVolumeFilter = true;          // Use Volume Filter
input double InpVolumeMultiplier = 1.5;        // Volume Multiplier
input bool InpUseSpreadFilter = true;          // Use Spread Filter
input int InpMaxSpread = 30;                   // Maximum Spread (points)
input bool InpUseMomentumFilter = true;        // Use Momentum Filter
input int InpMomentumPeriod = 5;               // Momentum Period
input bool InpUseTimeFilter = true;           // Use Time Filter
input string InpStartTime = "07:00";           // Trading Start Time
input string InpEndTime = "21:00";             // Trading End Time

input group "=== Dynamic Risk Management ==="
input double InpLotSize = 0.01;                // Base Lot Size
input bool InpUseDynamicLots = true;           // Use Dynamic Lot Sizing
input double InpRiskPercent = 1.0;             // Risk Percentage per Trade
input double InpTakeProfit = 15.0;             // Take Profit (Pips)
input double InpATR_Multiplier = 1.5;          // ATR Multiplier for Stop Loss
input bool InpUseTrailingStop = true;         // Use Trailing Stop
input double InpTrailingStart = 8.0;          // Trailing Start (Pips)
input double InpTrailingStep = 3.0;           // Trailing Step (Pips)

input group "=== Advanced Exit Strategy ==="
input bool InpUsePartialClose = true;         // Use Partial Close
input double InpPartialClosePercent = 50.0;   // Partial Close Percentage
input double InpPartialClosePips = 8.0;       // Partial Close at Pips
input bool InpUseBreakeven = true;            // Move to Breakeven
input double InpBreakevenPips = 6.0;          // Breakeven Trigger (Pips)

input group "=== Trade Management ==="
input bool InpAllowBuy = true;                 // Allow Buy Orders
input bool InpAllowSell = true;                // Allow Sell Orders
input int InpMaxPositions = 2;                 // Maximum Positions
input int InpMagicNumber = 789012;             // Magic Number

//--- Global variables
int handle_ema_fast, handle_ema_medium, handle_ema_slow, handle_rsi, handle_atr, handle_momentum;
double ema_fast[], ema_medium[], ema_slow[], rsi[], atr[], momentum[];
long tick_volume[];
datetime last_trade_time = 0;

//--- Trade management
CTrade trade;

//--- Structures for position tracking
struct PositionInfo
{
    ulong ticket;
    double entry_price;
    bool partial_closed;
    bool moved_to_breakeven;
};

PositionInfo active_positions[];

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Initialize indicators
    handle_ema_fast = iMA(_Symbol, PERIOD_CURRENT, InpEMA_Fast, 0, MODE_EMA, PRICE_CLOSE);
    handle_ema_medium = iMA(_Symbol, PERIOD_CURRENT, InpEMA_Medium, 0, MODE_EMA, PRICE_CLOSE);
    handle_ema_slow = iMA(_Symbol, PERIOD_CURRENT, InpEMA_Slow, 0, MODE_EMA, PRICE_CLOSE);
    handle_rsi = iRSI(_Symbol, PERIOD_CURRENT, InpRSI_Period, PRICE_CLOSE);
    handle_atr = iATR(_Symbol, PERIOD_CURRENT, InpATR_Period);
    handle_momentum = iMomentum(_Symbol, PERIOD_CURRENT, InpMomentumPeriod, PRICE_CLOSE);
    
    // Check if indicators are created successfully
    if(handle_ema_fast == INVALID_HANDLE || handle_ema_medium == INVALID_HANDLE || 
       handle_ema_slow == INVALID_HANDLE || handle_rsi == INVALID_HANDLE || 
       handle_atr == INVALID_HANDLE || handle_momentum == INVALID_HANDLE)
    {
        Print("Error creating indicators");
        return INIT_FAILED;
    }
    
    // Set magic number for trade class
    trade.SetExpertMagicNumber(InpMagicNumber);
    trade.SetDeviationInPoints(10);
    
    // Set array as series
    ArraySetAsSeries(ema_fast, true);
    ArraySetAsSeries(ema_medium, true);
    ArraySetAsSeries(ema_slow, true);
    ArraySetAsSeries(rsi, true);
    ArraySetAsSeries(atr, true);
    ArraySetAsSeries(momentum, true);
    ArraySetAsSeries(tick_volume, true);
    
    // Initialize position tracking array
    ArrayResize(active_positions, 0);
    
    Print("Enhanced Gold Scalping EA initialized successfully");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Release indicator handles
    IndicatorRelease(handle_ema_fast);
    IndicatorRelease(handle_ema_medium);
    IndicatorRelease(handle_ema_slow);
    IndicatorRelease(handle_rsi);
    IndicatorRelease(handle_atr);
    IndicatorRelease(handle_momentum);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Update position tracking
    UpdatePositionTracking();
    
    // Manage existing positions
    ManagePositions();
    
    // Check for new bar
    if(!IsNewBar()) return;
    
    // Apply filters
    if(!PassesFilters()) return;
    
    // Get current indicator values
    if(!GetIndicatorValues()) return;
    
    // Check current positions
    int current_positions = CountPositions();
    if(current_positions >= InpMaxPositions) return;
    
    // Prevent multiple trades in same minute
    if(TimeCurrent() - last_trade_time < 60) return;
    
    // Get current market data
    MqlRates rates[];
    if(CopyRates(_Symbol, PERIOD_CURRENT, 0, 5, rates) < 5) return;
    ArraySetAsSeries(rates, true);
    
    // Check for trade signals with enhanced logic
    CheckForEnhancedBuySignal(rates);
    CheckForEnhancedSellSignal(rates);
}

//+------------------------------------------------------------------+
//| Check if new bar formed                                          |
//+------------------------------------------------------------------+
bool IsNewBar()
{
    static datetime last_time = 0;
    datetime current_time = iTime(_Symbol, PERIOD_CURRENT, 0);
    
    if(current_time != last_time)
    {
        last_time = current_time;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| Apply various filters                                            |
//+------------------------------------------------------------------+
bool PassesFilters()
{
    // Time filter
    if(InpUseTimeFilter && !IsInTradingHours()) return false;
    
    // Spread filter
    if(InpUseSpreadFilter)
    {
        long spread = SymbolInfoInteger(_Symbol, SYMBOL_SPREAD);
        if(spread > InpMaxSpread) return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Check if current time is within trading hours                   |
//+------------------------------------------------------------------+
bool IsInTradingHours()
{
    datetime current_time = TimeCurrent();
    MqlDateTime dt;
    TimeToStruct(current_time, dt);
    
    int current_hour_minute = dt.hour * 100 + dt.min;
    int start_time = (int)StringToInteger(StringSubstr(InpStartTime, 0, 2)) * 100 + 
                     (int)StringToInteger(StringSubstr(InpStartTime, 3, 2));
    int end_time = (int)StringToInteger(StringSubstr(InpEndTime, 0, 2)) * 100 + 
                   (int)StringToInteger(StringSubstr(InpEndTime, 3, 2));
    
    return (current_hour_minute >= start_time && current_hour_minute <= end_time);
}

//+------------------------------------------------------------------+
//| Get indicator values                                             |
//+------------------------------------------------------------------+
bool GetIndicatorValues()
{
    // Copy indicator values
    if(CopyBuffer(handle_ema_fast, 0, 0, 5, ema_fast) < 5) return false;
    if(CopyBuffer(handle_ema_medium, 0, 0, 5, ema_medium) < 5) return false;
    if(CopyBuffer(handle_ema_slow, 0, 0, 5, ema_slow) < 5) return false;
    if(CopyBuffer(handle_rsi, 0, 0, 5, rsi) < 5) return false;
    if(CopyBuffer(handle_atr, 0, 0, 5, atr) < 5) return false;
    if(CopyBuffer(handle_momentum, 0, 0, 5, momentum) < 5) return false;
    if(CopyTickVolume(_Symbol, PERIOD_CURRENT, 0, 5, tick_volume) < 5) return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| Enhanced check for buy signal                                   |
//+------------------------------------------------------------------+
void CheckForEnhancedBuySignal(const MqlRates &rates[])
{
    if(!InpAllowBuy) return;
    
    // Multi-timeframe EMA alignment
    if(ema_fast[0] <= ema_medium[0] || ema_medium[0] <= ema_slow[0]) return;
    
    // Enhanced RSI condition with momentum
    if(rsi[0] < InpRSI_Lower || rsi[0] > InpRSI_Upper) return;
    if(rsi[0] <= rsi[1]) return; // RSI should be rising
    
    // Momentum filter
    if(InpUseMomentumFilter && momentum[0] <= momentum[1]) return;
    
    // Volume filter
    if(InpUseVolumeFilter)
    {
        long avg_volume = (tick_volume[1] + tick_volume[2] + tick_volume[3]) / 3;
        if(tick_volume[0] < avg_volume * InpVolumeMultiplier) return;
    }
    
    // Enhanced engulfing with additional confirmation
    if(!IsEnhancedBullishPattern(rates)) return;
    
    // Price should be above all EMAs
    if(rates[0].close <= ema_fast[0] || rates[0].close <= ema_medium[0]) return;
    
    // Additional confirmation: recent bullish momentum
    bool bullish_momentum = true;
    for(int i = 0; i < 3; i++)
    {
        if(rates[i].close < rates[i].open) bullish_momentum = false;
    }
    if(!bullish_momentum) return;
    
    // Execute buy order
    ExecuteEnhancedBuyOrder();
}

//+------------------------------------------------------------------+
//| Enhanced check for sell signal                                  |
//+------------------------------------------------------------------+
void CheckForEnhancedSellSignal(const MqlRates &rates[])
{
    if(!InpAllowSell) return;
    
    // Multi-timeframe EMA alignment
    if(ema_fast[0] >= ema_medium[0] || ema_medium[0] >= ema_slow[0]) return;
    
    // Enhanced RSI condition with momentum
    if(rsi[0] < InpRSI_Lower || rsi[0] > InpRSI_Upper) return;
    if(rsi[0] >= rsi[1]) return; // RSI should be falling
    
    // Momentum filter
    if(InpUseMomentumFilter && momentum[0] >= momentum[1]) return;
    
    // Volume filter
    if(InpUseVolumeFilter)
    {
        long avg_volume = (tick_volume[1] + tick_volume[2] + tick_volume[3]) / 3;
        if(tick_volume[0] < avg_volume * InpVolumeMultiplier) return;
    }
    
    // Enhanced engulfing with additional confirmation
    if(!IsEnhancedBearishPattern(rates)) return;
    
    // Price should be below all EMAs
    if(rates[0].close >= ema_fast[0] || rates[0].close >= ema_medium[0]) return;
    
    // Additional confirmation: recent bearish momentum
    bool bearish_momentum = true;
    for(int i = 0; i < 3; i++)
    {
        if(rates[i].close > rates[i].open) bearish_momentum = false;
    }
    if(!bearish_momentum) return;
    
    // Execute sell order
    ExecuteEnhancedSellOrder();
}

//+------------------------------------------------------------------+
//| Enhanced bullish pattern recognition                            |
//+------------------------------------------------------------------+
bool IsEnhancedBullishPattern(const MqlRates &rates[])
{
    // Classic bullish engulfing
    if(!IsBullishEngulfing(rates)) return false;
    
    // Additional patterns
    bool hammer = (rates[0].close > rates[0].open) && 
                  ((rates[0].close - rates[0].open) > 2 * (rates[0].open - rates[0].low)) &&
                  (rates[0].high - rates[0].close) < (rates[0].close - rates[0].open) * 0.3;
    
    bool morning_star = (rates[2].close < rates[2].open) && // Bearish candle
                        (MathAbs(rates[1].close - rates[1].open) < (rates[2].open - rates[2].close) * 0.3) && // Doji
                        (rates[0].close > rates[0].open) && // Bullish candle
                        (rates[0].close > (rates[2].open + rates[2].close) / 2); // Closes above midpoint
    
    return (hammer || morning_star);
}

//+------------------------------------------------------------------+
//| Enhanced bearish pattern recognition                            |
//+------------------------------------------------------------------+
bool IsEnhancedBearishPattern(const MqlRates &rates[])
{
    // Classic bearish engulfing
    if(!IsBearishEngulfing(rates)) return false;
    
    // Additional patterns
    bool shooting_star = (rates[0].close < rates[0].open) && 
                         ((rates[0].open - rates[0].close) > 2 * (rates[0].high - rates[0].open)) &&
                         (rates[0].close - rates[0].low) < (rates[0].open - rates[0].close) * 0.3;
    
    bool evening_star = (rates[2].close > rates[2].open) && // Bullish candle
                        (MathAbs(rates[1].close - rates[1].open) < (rates[2].close - rates[2].open) * 0.3) && // Doji
                        (rates[0].close < rates[0].open) && // Bearish candle
                        (rates[0].close < (rates[2].open + rates[2].close) / 2); // Closes below midpoint
    
    return (shooting_star || evening_star);
}

//+------------------------------------------------------------------+
//| Check for bullish engulfing pattern                             |
//+------------------------------------------------------------------+
bool IsBullishEngulfing(const MqlRates &rates[])
{
    return (rates[0].close > rates[0].open) && 
           (rates[1].close < rates[1].open) && 
           (rates[0].open < rates[1].close) && 
           (rates[0].close > rates[1].open);
}

//+------------------------------------------------------------------+
//| Check for bearish engulfing pattern                             |
//+------------------------------------------------------------------+
bool IsBearishEngulfing(const MqlRates &rates[])
{
    return (rates[0].close < rates[0].open) && 
           (rates[1].close > rates[1].open) && 
           (rates[0].open > rates[1].close) && 
           (rates[0].close < rates[1].open);
}

//+------------------------------------------------------------------+
//| Calculate dynamic lot size                                       |
//+------------------------------------------------------------------+
double CalculateLotSize(double stop_loss_distance)
{
    if(!InpUseDynamicLots) return InpLotSize;
    
    double account_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double risk_amount = account_balance * InpRiskPercent / 100.0;
    
    double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    
    double lot_size = risk_amount / (stop_loss_distance / tick_size * tick_value);
    
    // Normalize lot size
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    
    lot_size = MathMax(min_lot, MathMin(max_lot, MathRound(lot_size / lot_step) * lot_step));
    
    return lot_size;
}

//+------------------------------------------------------------------+
//| Execute enhanced buy order                                       |
//+------------------------------------------------------------------+
void ExecuteEnhancedBuyOrder()
{
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);
    
    // Calculate stop loss based on ATR
    double stop_loss = ask - (atr[0] * InpATR_Multiplier);
    double take_profit = ask + (InpTakeProfit * point * 10);
    
    // Calculate dynamic lot size
    double lot_size = CalculateLotSize(ask - stop_loss);
    
    // Normalize prices
    stop_loss = NormalizeDouble(stop_loss, digits);
    take_profit = NormalizeDouble(take_profit, digits);
    
    // Execute buy order
    if(trade.Buy(lot_size, _Symbol, ask, stop_loss, take_profit, "Enhanced Gold Buy"))
    {
        last_trade_time = TimeCurrent();
        
        // Add to position tracking
        PositionInfo pos;
        pos.ticket = trade.ResultOrder();
        pos.entry_price = ask;
        pos.partial_closed = false;
        pos.moved_to_breakeven = false;
        
        int size = ArraySize(active_positions);
        ArrayResize(active_positions, size + 1);
        active_positions[size] = pos;
        
        Print("Enhanced Buy order executed at ", ask, " SL: ", stop_loss, " TP: ", take_profit, " Lot: ", lot_size);
    }
}

//+------------------------------------------------------------------+
//| Execute enhanced sell order                                      |
//+------------------------------------------------------------------+
void ExecuteEnhancedSellOrder()
{
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);
    
    // Calculate stop loss based on ATR
    double stop_loss = bid + (atr[0] * InpATR_Multiplier);
    double take_profit = bid - (InpTakeProfit * point * 10);
    
    // Calculate dynamic lot size
    double lot_size = CalculateLotSize(stop_loss - bid);
    
    // Normalize prices
    stop_loss = NormalizeDouble(stop_loss, digits);
    take_profit = NormalizeDouble(take_profit, digits);
    
    // Execute sell order
    if(trade.Sell(lot_size, _Symbol, bid, stop_loss, take_profit, "Enhanced Gold Sell"))
    {
        last_trade_time = TimeCurrent();
        
        // Add to position tracking
        PositionInfo pos;
        pos.ticket = trade.ResultOrder();
        pos.entry_price = bid;
        pos.partial_closed = false;
        pos.moved_to_breakeven = false;
        
        int size = ArraySize(active_positions);
        ArrayResize(active_positions, size + 1);
        active_positions[size] = pos;
        
        Print("Enhanced Sell order executed at ", bid, " SL: ", stop_loss, " TP: ", take_profit, " Lot: ", lot_size);
    }
}

//+------------------------------------------------------------------+
//| Update position tracking array                                   |
//+------------------------------------------------------------------+
void UpdatePositionTracking()
{
    for(int i = ArraySize(active_positions) - 1; i >= 0; i--)
    {
        if(!PositionSelectByTicket(active_positions[i].ticket))
        {
            // Position closed, remove from tracking
            for(int j = i; j < ArraySize(active_positions) - 1; j++)
            {
                active_positions[j] = active_positions[j + 1];
            }
            ArrayResize(active_positions, ArraySize(active_positions) - 1);
        }
    }
}

//+------------------------------------------------------------------+
//| Manage existing positions                                        |
//+------------------------------------------------------------------+
void ManagePositions()
{
    double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
    
    for(int i = 0; i < ArraySize(active_positions); i++)
    {
        if(!PositionSelectByTicket(active_positions[i].ticket)) continue;
        
        double current_price = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? 
                              SymbolInfoDouble(_Symbol, SYMBOL_BID) : 
                              SymbolInfoDouble(_Symbol, SYMBOL_ASK);
        
        double entry_price = active_positions[i].entry_price;
        double profit_pips = 0;
        
        if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
            profit_pips = (current_price - entry_price) / (point * 10);
        else
            profit_pips = (entry_price - current_price) / (point * 10);
        
        // Partial close
        if(InpUsePartialClose && !active_positions[i].partial_closed && profit_pips >= InpPartialClosePips)
        {
            double current_volume = PositionGetDouble(POSITION_VOLUME);
            double close_volume = current_volume * InpPartialClosePercent / 100.0;
            
            if(trade.PositionClosePartial(active_positions[i].ticket, close_volume))
            {
                active_positions[i].partial_closed = true;
                Print("Partial close executed for ticket: ", active_positions[i].ticket);
            }
        }
        
        // Move to breakeven
        if(InpUseBreakeven && !active_positions[i].moved_to_breakeven && profit_pips >= InpBreakevenPips)
        {
            double new_sl = entry_price;
            if(trade.PositionModify(active_positions[i].ticket, new_sl, PositionGetDouble(POSITION_TP)))
            {
                active_positions[i].moved_to_breakeven = true;
                Print("Position moved to breakeven for ticket: ", active_positions[i].ticket);
            }
        }
        
        // Trailing stop
        if(InpUseTrailingStop && profit_pips >= InpTrailingStart)
        {
            double current_sl = PositionGetDouble(POSITION_SL);
            double new_sl = 0;
            
            if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
            {
                new_sl = current_price - (InpTrailingStep * point * 10);
                if(new_sl > current_sl && new_sl < current_price)
                {
                    trade.PositionModify(active_positions[i].ticket, new_sl, PositionGetDouble(POSITION_TP));
                }
            }
            else
            {
                new_sl = current_price + (InpTrailingStep * point * 10);
                if((current_sl == 0 || new_sl < current_sl) && new_sl > current_price)
                {
                    trade.PositionModify(active_positions[i].ticket, new_sl, PositionGetDouble(POSITION_TP));
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Count current positions                                          |
//+------------------------------------------------------------------+
int CountPositions()
{
    int count = 0;
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionSelectByTicket(PositionGetTicket(i)))
        {
            if(PositionGetString(POSITION_SYMBOL) == _Symbol && 
               PositionGetInteger(POSITION_MAGIC) == InpMagicNumber)
            {
                count++;
            }
        }
    }
    return count;
}

//+------------------------------------------------------------------+
//| Trade transaction function                                       |
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,
                       const MqlTradeRequest& request,
                       const MqlTradeResult& result)
{
    if(trans.symbol == _Symbol && trans.magic == InpMagicNumber)
    {
        if(trans.type == TRADE_TRANSACTION_DEAL_ADD)
        {
            Print("Enhanced trade executed: ", trans.symbol, " Volume: ", trans.volume, " Price: ", trans.price);
        }
    }
}
